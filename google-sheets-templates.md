# Google Sheets Templates for Universal Blog Automation

## 1. Active_Clients Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I | J | K |
|---|---|---|---|---|---|---|---|---|---|---|
| business_name | industry | city | state | target_keywords | brand_voice | services | wordpress_url | wordpress_user | wordpress_password | status |

### Example Data (Row 2+):
```
A2: Raga Dental Thanjavur
B2: Healthcare - Dental
C2: Thanjavur
D2: Tamil Nadu
E2: dental implants, teeth whitening, root canal treatment
F2: Professional and caring
G2: Dental implants, Cosmetic dentistry, Preventive care
H2: https://ragadental.com
I2: admin
J2: your_wp_password
K2: Active

A3: Phoenix HVAC Solutions
B3: Home Services - HVAC
C3: Phoenix
D3: Arizona
E3: AC repair, heating installation, HVAC maintenance
F3: Reliable and expert
G3: Air conditioning repair, Heating systems, Maintenance
H3: https://phoenixhvac.com
I3: admin
J3: your_wp_password
K3: Active

A4: Miami Legal Associates
B4: Legal Services
C4: Miami
D4: Florida
E4: personal injury lawyer, car accident attorney, legal consultation
F4: Authoritative and trustworthy
G4: Personal injury, Auto accidents, Legal consultation
H4: https://miamilegal.com
I4: admin
J4: your_wp_password
K4: Active
```

## 2. Published_Content Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| date_published | business_name | title | primary_keyword | secondary_keywords | url | status | search_intent | word_count | performance_notes |

### Example Data (Row 2+):
```
A2: 2024-01-15
B2: Raga Dental Thanjavur
C2: Complete Guide to Dental Implants in Thanjavur
D2: dental implants Thanjavur
E2: tooth replacement, implant surgery, dental restoration
F2: https://ragadental.com/dental-implants-thanjavur-guide
G2: Published
H2: Commercial
I2: 2,450
J2: Tracking rankings

A3: 2024-01-15
B3: Phoenix HVAC Solutions
C3: Top 10 Signs Your AC Needs Repair in Phoenix
D3: AC repair Phoenix
E3: air conditioning problems, HVAC maintenance, cooling issues
F3: https://phoenixhvac.com/ac-repair-signs-phoenix
G3: Published
H3: Informational
I3: 2,200
J3: Monitoring traffic
```

## 3. Keyword_Research Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I |
|---|---|---|---|---|---|---|---|---|
| business_name | keyword | search_volume | difficulty | cpc | competition | intent | last_updated | notes |

### Example Data (Row 2+):
```
A2: Raga Dental Thanjavur
B2: dental implants Thanjavur
C2: 480
D2: 35
E2: $3.20
F2: Medium
G2: Commercial
H2: 2024-01-15
I2: High conversion potential

A3: Raga Dental Thanjavur
B3: best dentist Thanjavur
C3: 720
D3: 28
E3: $2.80
F3: Low
G3: Commercial
H3: 2024-01-15
I3: Brand awareness focus
```

## 4. Performance_Tracking Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I | J | K |
|---|---|---|---|---|---|---|---|---|---|---|
| date | business_name | keyword | position | search_volume | url | clicks | impressions | ctr | avg_position | notes |

### Example Data (Row 2+):
```
A2: 2024-01-15
B2: Raga Dental Thanjavur
C2: dental implants Thanjavur
D2: 3
E2: 480
F2: https://ragadental.com/dental-implants-thanjavur-guide
G2: 45
H2: 1,200
I2: 3.75%
J2: 3.2
K2: Improving steadily

A3: 2024-01-15
B3: Phoenix HVAC Solutions
C3: AC repair Phoenix
D3: 7
E3: 2,400
F3: https://phoenixhvac.com/ac-repair-signs-phoenix
G3: 89
H3: 3,200
I3: 2.78%
J3: 6.8
K3: Need more backlinks
```

## 5. Content_Calendar Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I |
|---|---|---|---|---|---|---|---|---|
| scheduled_date | business_name | topic_status | content_type | target_keyword | title | content_angle | priority | notes |

### Example Data (Row 2+):
```
A2: 2024-01-20
B2: Raga Dental Thanjavur
C2: Planned
D2: How-to Guide
E2: teeth whitening Thanjavur
F2: Professional Teeth Whitening vs At-Home Methods
G2: Cost comparison with local pricing
H2: High
I2: Include before/after photos

A3: 2024-01-22
B3: Phoenix HVAC Solutions
C3: In Progress
D3: Seasonal Content
E3: summer AC maintenance Phoenix
F3: Prepare Your AC for Phoenix Summer Heat
G3: Preventive maintenance focus
H3: High
I3: Seasonal urgency - publish before April
```

## 6. Client_Settings Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| business_name | posting_frequency | content_length | preferred_topics | avoid_topics | seasonal_focus | local_events | competitor_urls | special_offers | contact_info |

### Example Data (Row 2+):
```
A2: Raga Dental Thanjavur
B2: 2 posts/week
C2: 2000-2500 words
D2: Dental procedures, Oral health, Patient testimonials
E2: Controversial treatments, Pricing discussions
F2: Wedding season, Festival times
G2: Thanjavur cultural events, Medical conferences
H2: https://competitor1.com, https://competitor2.com
I2: Free consultation, 20% off whitening
J2: Dr. Raga, +91-***********, <EMAIL>

A3: Phoenix HVAC Solutions
B3: 3 posts/week
C3: 1800-2200 words
D3: HVAC maintenance, Energy efficiency, Seasonal tips
E3: DIY repairs, Competitor comparisons
F3: Summer cooling, Winter heating
G3: Phoenix home shows, Energy efficiency events
H3: https://hvaccompetitor1.com, https://hvaccompetitor2.com
I3: Free estimates, 10% off maintenance plans
J3: Mike Johnson, (602) 555-0123, <EMAIL>
```

## 7. SEO_Metrics Sheet Structure

### Column Headers (Row 1):
| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| date | business_name | organic_traffic | keyword_rankings | backlinks | domain_authority | local_pack_appearances | conversion_rate | roi | notes |

### Example Data (Row 2+):
```
A2: 2024-01-15
B2: Raga Dental Thanjavur
C2: 1,250
D2: 15 keywords in top 10
E2: 45
F2: 28
G2: 3 services in local pack
H2: 4.2%
I2: 320%
J2: Strong growth month

A3: 2024-01-15
B3: Phoenix HVAC Solutions
C3: 2,800
D3: 22 keywords in top 10
E3: 67
F3: 35
G3: 5 services in local pack
H3: 6.1%
I3: 450%
J3: Seasonal peak performance
```

## Setup Instructions:

### 1. Create Google Sheets Document
1. Go to Google Sheets
2. Create new spreadsheet named "Universal Blog Automation - [Your Business]"
3. Create 7 sheets with the names above
4. Copy the column headers exactly as shown
5. Add sample data for testing

### 2. Configure Sheet IDs in n8n
1. Get the Google Sheets document ID from the URL
2. Replace `CLIENT_CONFIG_SHEET_ID` with your actual sheet ID
3. Replace `TRACKING_SHEET_ID` with your actual sheet ID
4. Ensure sheet names match exactly (case-sensitive)

### 3. Set Up Google Sheets API
1. Enable Google Sheets API in Google Cloud Console
2. Create OAuth2 credentials
3. Configure in n8n credentials manager
4. Test connection with a simple read operation

### 4. Data Validation Rules
- **Status column**: Dropdown with "Active", "Paused", "Inactive"
- **Brand Voice**: Dropdown with "Professional", "Friendly", "Authoritative", "Casual"
- **Industry**: Standardized industry categories
- **Priority**: Dropdown with "High", "Medium", "Low"

### 5. Automation Features
- **Conditional Formatting**: Highlight overdue content, high-priority items
- **Data Validation**: Ensure consistent data entry
- **Formulas**: Auto-calculate ROI, performance metrics
- **Charts**: Visual dashboards for client performance

This structure allows the n8n workflow to automatically process multiple clients, track performance, and scale efficiently while maintaining data organization and reporting capabilities.
