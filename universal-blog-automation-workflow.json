{"name": "Universal Blog Automation System", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2400, 200]}, {"parameters": {"documentId": {"__rl": true, "value": "CLIENT_CONFIG_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "Active_Clients", "mode": "list"}, "options": {}}, "id": "load-client-configs", "name": "Load Client Configurations", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2200, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-clients", "name": "Split Into Batches", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2000, 200]}, {"parameters": {"assignments": {"assignments": [{"id": "business-name", "name": "business_name", "type": "string", "value": "={{ $json.business_name }}"}, {"id": "industry", "name": "industry", "type": "string", "value": "={{ $json.industry }}"}, {"id": "location", "name": "location", "type": "string", "value": "={{ $json.city }}, {{ $json.state }}"}, {"id": "target-keywords", "name": "target_keywords", "type": "string", "value": "={{ $json.target_keywords }}"}, {"id": "brand-voice", "name": "brand_voice", "type": "string", "value": "={{ $json.brand_voice }}"}, {"id": "services", "name": "services", "type": "string", "value": "={{ $json.services }}"}]}}, "id": "set-client-context", "name": "Set Client Context", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1800, 200]}, {"parameters": {"method": "POST", "url": "https://api.semrush.com/analytics/v1/", "authentication": "predefinedCredentialType", "nodeCredentialType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "type", "value": "phrase_organic"}, {"name": "key", "value": "={{ $json.target_keywords }}"}, {"name": "display_limit", "value": "50"}, {"name": "database", "value": "us"}]}}, "id": "keyword-research", "name": "Keyword Research API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-1600, 200], "credentials": {"semrushApi": {"id": "semrush-api-key", "name": "SEMrush API"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o"}, "promptType": "define", "text": "=You are an expert SEO content strategist for {{ $('Set Client Context').item.json.business_name }} in {{ $('Set Client Context').item.json.location }}.\n\n**Business Context:**\n- Industry: {{ $('Set Client Context').item.json.industry }}\n- Services: {{ $('Set Client Context').item.json.services }}\n- Location: {{ $('Set Client Context').item.json.location }}\n- Brand Voice: {{ $('Set Client Context').item.json.brand_voice }}\n- Target Keywords: {{ $('Set Client Context').item.json.target_keywords }}\n\n**Available Keywords from Research:**\n{{ $('Keyword Research API').item.json }}\n\n**Task:** Generate a NEW blog topic that:\n1. Targets high-commercial-intent keywords for {{ $('Set Client Context').item.json.industry }}\n2. Addresses specific pain points of {{ $('Set Client Context').item.json.industry }} customers\n3. Incorporates local SEO for {{ $('Set Client Context').item.json.location }} area\n4. Has strong ranking potential (low competition, high search volume)\n5. Is unique and not generic\n\n**Output Format:**\n{\n  \"topic\": \"[Specific, unique topic]\",\n  \"primary_keyword\": \"[Main target keyword]\",\n  \"secondary_keywords\": [\"keyword1\", \"keyword2\", \"keyword3\"],\n  \"search_intent\": \"[informational/commercial/transactional]\",\n  \"content_angle\": \"[unique approach/perspective]\",\n  \"target_audience_segment\": \"[specific audience]\"\n}\n\nReturn only the JSON object with the perfect blog title as the final output.", "options": {}}, "id": "topic-generator", "name": "AI Topic Generator", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1400, 200]}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o"}, "options": {}}, "id": "openai-model-topic", "name": "OpenAI Model - Topic", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1400, 400], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"topic\": \"topic\",\n  \"primary_keyword\": \"keyword\",\n  \"secondary_keywords\": [\"keyword1\", \"keyword2\"],\n  \"search_intent\": \"intent\",\n  \"content_angle\": \"angle\",\n  \"target_audience_segment\": \"audience\"\n}"}, "id": "topic-json-parser", "name": "Topic JSON Parser", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-1200, 400], "typeVersion": 1.2}], "connections": {"Schedule Trigger": {"main": [[{"node": "Load Client Configurations", "type": "main", "index": 0}]]}, "Load Client Configurations": {"main": [[{"node": "Split Into Batches", "type": "main", "index": 0}]]}, "Split Into Batches": {"main": [[{"node": "Set Client Context", "type": "main", "index": 0}]]}, "Set Client Context": {"main": [[{"node": "Keyword Research API", "type": "main", "index": 0}]]}, "Keyword Research API": {"main": [[{"node": "AI Topic Generator", "type": "main", "index": 0}]]}, "OpenAI Model - Topic": {"ai_languageModel": [[{"node": "AI Topic Generator", "type": "ai_languageModel", "index": 0}]]}, "Topic JSON Parser": {"ai_outputParser": [[{"node": "AI Topic Generator", "type": "ai_outputParser", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "universal-blog-automation-v1"}}