{"name": "Universal Blog Automation System - Complete", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "id": "schedule-trigger", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2400, 200]}, {"parameters": {"documentId": {"__rl": true, "value": "CLIENT_CONFIG_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "Active_Clients", "mode": "list"}, "options": {}}, "id": "load-client-configs", "name": "Load Client Configurations", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-2200, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-clients", "name": "Split Into Batches", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2000, 200]}, {"parameters": {"assignments": {"assignments": [{"id": "business-name", "name": "business_name", "type": "string", "value": "={{ $json.business_name }}"}, {"id": "industry", "name": "industry", "type": "string", "value": "={{ $json.industry }}"}, {"id": "location", "name": "location", "type": "string", "value": "={{ $json.city }}, {{ $json.state }}"}, {"id": "target-keywords", "name": "target_keywords", "type": "string", "value": "={{ $json.target_keywords }}"}, {"id": "brand-voice", "name": "brand_voice", "type": "string", "value": "={{ $json.brand_voice }}"}, {"id": "services", "name": "services", "type": "string", "value": "={{ $json.services }}"}, {"id": "wordpress-url", "name": "wordpress_url", "type": "string", "value": "={{ $json.wordpress_url }}"}, {"id": "wordpress-user", "name": "wordpress_user", "type": "string", "value": "={{ $json.wordpress_user }}"}, {"id": "wordpress-password", "name": "wordpress_password", "type": "string", "value": "={{ $json.wordpress_password }}"}]}}, "id": "set-client-context", "name": "Set Client Context", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1800, 200]}, {"parameters": {"method": "POST", "url": "https://api.semrush.com/analytics/v1/", "authentication": "predefinedCredentialType", "nodeCredentialType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "type", "value": "phrase_organic"}, {"name": "key", "value": "={{ $json.target_keywords }}"}, {"name": "display_limit", "value": "50"}, {"name": "database", "value": "us"}]}}, "id": "keyword-research", "name": "Keyword Research API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-1600, 200], "credentials": {"semrushApi": {"id": "semrush-api-key", "name": "SEMrush API"}}}, {"parameters": {"resource": "chat", "model": "gpt-4o", "messages": {"values": [{"content": "=You are an expert SEO content strategist for {{ $('Set Client Context').item.json.business_name }} in {{ $('Set Client Context').item.json.location }}.\n\n**Business Context:**\n- Industry: {{ $('Set Client Context').item.json.industry }}\n- Services: {{ $('Set Client Context').item.json.services }}\n- Location: {{ $('Set Client Context').item.json.location }}\n- Brand Voice: {{ $('Set Client Context').item.json.brand_voice }}\n- Target Keywords: {{ $('Set Client Context').item.json.target_keywords }}\n\n**Available Keywords from Research:**\n{{ $('Keyword Research API').item.json }}\n\n**Task:** Generate a NEW blog topic that:\n1. Targets high-commercial-intent keywords for {{ $('Set Client Context').item.json.industry }}\n2. Addresses specific pain points of {{ $('Set Client Context').item.json.industry }} customers\n3. Incorporates local SEO for {{ $('Set Client Context').item.json.location }} area\n4. Has strong ranking potential (low competition, high search volume)\n5. Is unique and not generic\n\n**Output Format:**\n{\n  \"topic\": \"[Specific, unique topic]\",\n  \"primary_keyword\": \"[Main target keyword]\",\n  \"secondary_keywords\": [\"keyword1\", \"keyword2\", \"keyword3\"],\n  \"search_intent\": \"[informational/commercial/transactional]\",\n  \"content_angle\": \"[unique approach/perspective]\",\n  \"target_audience_segment\": \"[specific audience]\"\n}\n\nReturn only the JSON object.", "role": "user"}]}, "options": {"temperature": 0.7}}, "id": "topic-generator", "name": "AI Topic Generator", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [-1400, 200], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "topic_data", "value": "={{ JSON.parse($json.choices[0].message.content) }}"}]}}, "id": "parse-topic-json", "name": "Parse Topic JSON", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1200, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "messages": {"values": [{"content": "=You are a world-class content writer specializing in {{ $('Set Client Context').item.json.industry }} with deep expertise in {{ $('Set Client Context').item.json.services }}.\n\n**Content Brief:**\n- Topic: {{ $('Parse Topic JSON').item.json.topic_data.topic }}\n- Primary Keyword: {{ $('Parse Topic JSON').item.json.topic_data.primary_keyword }}\n- Secondary Keywords: {{ $('Parse Topic JSON').item.json.topic_data.secondary_keywords }}\n- Business: {{ $('Set Client Context').item.json.business_name }} in {{ $('Set Client Context').item.json.location }}\n- Brand Voice: {{ $('Set Client Context').item.json.brand_voice }}\n- Target Audience: {{ $('Parse Topic JSON').item.json.topic_data.target_audience_segment }}\n- Content Angle: {{ $('Parse Topic JSON').item.json.topic_data.content_angle }}\n\n**SEO Requirements:**\n- Primary Keyword: {{ $('Parse Topic JSON').item.json.topic_data.primary_keyword }} (use 3-5 times naturally)\n- Secondary Keywords: {{ $('Parse Topic JSON').item.json.topic_data.secondary_keywords }} (use 1-2 times each)\n- Local SEO: Include {{ $('Set Client Context').item.json.location }} references naturally\n- Word Count: 2000-2500 words\n\n**Content Structure:**\n1. **Compelling Hook** (150-200 words)\n   - Start with a question or statistic\n   - Address reader's pain point\n   - Preview value proposition\n\n2. **Main Content** (6-8 sections, 300-400 words each)\n   - Use benefit-driven H2 headings\n   - Include industry-specific examples\n   - Add local references and case studies\n   - Incorporate expert insights and statistics\n   - Use varied sentence structures and lengths\n\n3. **Actionable Conclusion** (200-250 words)\n   - Summarize key takeaways\n   - Include clear call-to-action for {{ $('Set Client Context').item.json.business_name }}\n   - Local contact information\n\n**Humanization Requirements:**\n- Use conversational tone with contractions\n- Include rhetorical questions\n- Add personal anecdotes or client stories\n- Use transitional phrases between sections\n- Vary paragraph lengths (2-5 sentences)\n- Include industry-specific terminology\n- Reference current trends when relevant\n\n**Output Format:**\n{\n  \"title\": \"[SEO-optimized title under 60 characters]\",\n  \"meta_description\": \"[Compelling description under 160 characters]\",\n  \"content\": \"[Full HTML-formatted article]\",\n  \"internal_links\": [\"link1\", \"link2\", \"link3\"],\n  \"faq_section\": [{\"question\": \"Q1\", \"answer\": \"A1\"}],\n  \"image_suggestions\": [\"image1_alt\", \"image2_alt\"],\n  \"target_keywords_used\": [\"keyword1\", \"keyword2\"]\n}\n\nCreate content that reads naturally, provides genuine value, and establishes {{ $('Set Client Context').item.json.business_name }} as the local authority in {{ $('Set Client Context').item.json.industry }}. Return only the JSON object.", "role": "user"}]}, "options": {"temperature": 0.8}}, "id": "content-generator", "name": "AI Content Generator", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [-1000, 200], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "content_data", "value": "={{ JSON.parse($json.choices[0].message.content) }}"}]}}, "id": "parse-content-json", "name": "Parse Content JSON", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-800, 200]}, {"parameters": {"resource": "chat", "model": "gpt-4o", "messages": {"values": [{"content": "=You are a professional editor specializing in making AI-generated content indistinguishable from human writing.\n\n**Original Content:** {{ $('Parse Content JSON').item.json.content_data.content }}\n\n**Business Context:**\n- Industry: {{ $('Set Client Context').item.json.industry }}\n- Location: {{ $('Set Client Context').item.json.location }}\n- Brand Voice: {{ $('Set Client Context').item.json.brand_voice }}\n- Target Audience: {{ $('Parse Topic JSON').item.json.topic_data.target_audience_segment }}\n\n**Humanization Tasks:**\n\n1. **Sentence Structure Variation:**\n   - Mix short (5-10 words), medium (11-20 words), and long (21+ words) sentences\n   - Use different sentence types: declarative, interrogative, exclamatory\n   - Add compound and complex sentences\n   - Include sentence fragments for emphasis\n\n2. **Natural Language Patterns:**\n   - Add contractions (don't, won't, it's, we're)\n   - Use colloquial expressions appropriate for {{ $('Set Client Context').item.json.brand_voice }}\n   - Include filler words occasionally (actually, really, quite)\n   - Add transitional phrases (however, meanwhile, in fact)\n\n3. **Human Elements:**\n   - Insert personal anecdotes or client stories\n   - Add local references specific to {{ $('Set Client Context').item.json.location }}\n   - Include current events or trends in {{ $('Set Client Context').item.json.industry }}\n   - Use rhetorical questions to engage readers\n   - Add parenthetical thoughts or asides\n\n4. **Industry Authenticity:**\n   - Use industry-specific jargon naturally\n   - Include insider knowledge or tips\n   - Reference common challenges in {{ $('Set Client Context').item.json.industry }}\n   - Add specific examples or case studies\n\n5. **Emotional Connection:**\n   - Use emotional language appropriate to topic\n   - Add empathy for reader's situation\n   - Include excitement or enthusiasm where appropriate\n   - Use power words and sensory language\n\n6. **Structural Improvements:**\n   - Vary paragraph lengths (1-6 sentences)\n   - Use bullet points and numbered lists\n   - Add subheadings that sound conversational\n   - Include call-out boxes or quotes\n\n**Output:** Rewrite the content to sound completely human-written while maintaining all SEO elements and key information. The final content should pass AI detection tools and feel authentic to readers.\n\nReturn only the humanized HTML content without any JSON formatting.", "role": "user"}]}, "options": {"temperature": 0.9}}, "id": "humanize-content", "name": "Humanize Content", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [-600, 200], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"resource": "image", "model": "dall-e-3", "prompt": "=Create a professional, high-quality featured image for a blog post about {{ $('Parse Topic JSON').item.json.topic_data.topic }} for {{ $('Set Client Context').item.json.business_name }} in {{ $('Set Client Context').item.json.location }}. The image should be:\n\n- Professional and modern design\n- Relevant to {{ $('Set Client Context').item.json.industry }} industry\n- Include subtle branding elements\n- High contrast and readable\n- Suitable for {{ $('Parse Topic JSON').item.json.topic_data.target_audience_segment }}\n- Local context for {{ $('Set Client Context').item.json.location }} if appropriate\n- Clean, minimalist style\n- 16:9 aspect ratio\n- No text overlays\n\nStyle: Professional photography or clean illustration, bright and engaging, suitable for business blog", "size": "1792x1024", "quality": "hd", "style": "natural"}, "id": "generate-featured-image", "name": "Generate Featured Image", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [-400, 200], "credentials": {"openAiApi": {"id": "openai-api-key", "name": "OpenAI API"}}}, {"parameters": {"method": "GET", "url": "={{ $json.data[0].url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "id": "download-image", "name": "Download Image", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-200, 200]}, {"parameters": {"url": "={{ $('Set Client Context').item.json.wordpress_url }}/wp-json/wp/v2/media", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "=attachment; filename=\"{{ $('Parse Topic JSON').item.json.topic_data.primary_keyword.replace(/\\s+/g, '-').toLowerCase() }}-featured-image.png\""}]}, "sendBody": true, "contentType": "binaryData", "options": {}}, "id": "upload-featured-image", "name": "Upload Featured Image", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [0, 200], "credentials": {"httpBasicAuth": {"id": "wordpress-auth", "name": "WordPress Basic Auth"}}}, {"parameters": {"url": "={{ $('Set Client Context').item.json.wordpress_url }}/wp-json/wp/v2/posts", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $('Parse Content JSON').item.json.content_data.title }}"}, {"name": "content", "value": "={{ $('Humanize Content').item.json.choices[0].message.content }}"}, {"name": "status", "value": "publish"}, {"name": "featured_media", "value": "={{ $('Upload Featured Image').item.json.id }}"}, {"name": "meta", "value": "={{ { \"_yoast_wpseo_metadesc\": $('Parse Content JSON').item.json.content_data.meta_description, \"_yoast_wpseo_focuskw\": $('Parse Topic JSON').item.json.topic_data.primary_keyword } }}"}, {"name": "categories", "value": "[1]"}, {"name": "tags", "value": "={{ $('Parse Topic JSON').item.json.topic_data.secondary_keywords.map(keyword => keyword.replace(/\\s+/g, '-').toLowerCase()) }}"}]}}, "id": "publish-to-wordpress", "name": "Publish to WordPress", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [200, 200], "credentials": {"httpBasicAuth": {"id": "wordpress-auth", "name": "WordPress Basic Auth"}}}, {"parameters": {"documentId": {"__rl": true, "value": "TRACKING_SHEET_ID", "mode": "list"}, "sheetName": {"__rl": true, "value": "Published_Content", "mode": "list"}, "options": {"cellFormat": "USER_ENTERED", "useAppend": true}, "dataStartRow": 2, "keyRowIndex": 1, "values": {"values": [{"A": "={{ new Date().toISOString().split('T')[0] }}"}, {"B": "={{ $('Set Client Context').item.json.business_name }}"}, {"C": "={{ $('Parse Content JSON').item.json.content_data.title }}"}, {"D": "={{ $('Parse Topic JSON').item.json.topic_data.primary_keyword }}"}, {"E": "={{ $('Parse Topic JSON').item.json.topic_data.secondary_keywords.join(', ') }}"}, {"F": "={{ $('Publish to WordPress').item.json.link }}"}, {"G": "Published"}, {"H": "={{ $('Parse Topic JSON').item.json.topic_data.search_intent }}"}]}}, "id": "track-published-content", "name": "Track Published Content", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [400, 200], "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-oauth", "name": "Google Sheets OAuth"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "SLACK_CHANNEL_ID", "mode": "list"}, "text": "=🎉 **New Blog Post Published!**\n\n**Client:** {{ $('Set Client Context').item.json.business_name }}\n**Title:** {{ $('Parse Content JSON').item.json.content_data.title }}\n**Primary Keyword:** {{ $('Parse Topic JSON').item.json.topic_data.primary_keyword }}\n**URL:** {{ $('Publish to WordPress').item.json.link }}\n**Industry:** {{ $('Set Client Context').item.json.industry }}\n**Location:** {{ $('Set Client Context').item.json.location }}\n\n**SEO Details:**\n• Target Keywords: {{ $('Parse Topic JSON').item.json.topic_data.secondary_keywords.join(', ') }}\n• Search Intent: {{ $('Parse Topic JSON').item.json.topic_data.search_intent }}\n• Content Angle: {{ $('Parse Topic JSON').item.json.topic_data.content_angle }}\n\n**Next Steps:**\n• Monitor rankings in 24-48 hours\n• Track organic traffic growth\n• Check for indexing in Google Search Console\n\n✅ Content successfully humanized and published!", "otherOptions": {}}, "id": "slack-notification", "name": "Slack Notification", "type": "n8n-nodes-base.slack", "typeVersion": 2.2, "position": [600, 200], "credentials": {"slackOAuth2Api": {"id": "slack-oauth", "name": "<PERSON><PERSON><PERSON>"}}}], "connections": {"Schedule Trigger": {"main": [[{"node": "Load Client Configurations", "type": "main", "index": 0}]]}, "Load Client Configurations": {"main": [[{"node": "Split Into Batches", "type": "main", "index": 0}]]}, "Split Into Batches": {"main": [[{"node": "Set Client Context", "type": "main", "index": 0}]]}, "Set Client Context": {"main": [[{"node": "Keyword Research API", "type": "main", "index": 0}]]}, "Keyword Research API": {"main": [[{"node": "AI Topic Generator", "type": "main", "index": 0}]]}, "AI Topic Generator": {"main": [[{"node": "Parse Topic JSON", "type": "main", "index": 0}]]}, "Parse Topic JSON": {"main": [[{"node": "AI Content Generator", "type": "main", "index": 0}]]}, "AI Content Generator": {"main": [[{"node": "Parse Content JSON", "type": "main", "index": 0}]]}, "Parse Content JSON": {"main": [[{"node": "Humanize Content", "type": "main", "index": 0}, {"node": "Generate Featured Image", "type": "main", "index": 0}]]}, "Generate Featured Image": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "Upload Featured Image", "type": "main", "index": 0}]]}, "Upload Featured Image": {"main": [[{"node": "Publish to WordPress", "type": "main", "index": 0}]]}, "Humanize Content": {"main": [[{"node": "Publish to WordPress", "type": "main", "index": 0}]]}, "Publish to WordPress": {"main": [[{"node": "Track Published Content", "type": "main", "index": 0}]]}, "Track Published Content": {"main": [[{"node": "Slack Notification", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "universal-blog-automation-complete-v1"}}