{"name": "Dental Appointment Reminder System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "appointment-scheduled", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Appointment Scheduled Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "appointment-scheduled"}, {"parameters": {"values": {"string": [{"name": "patient_name", "value": "={{ $json.patient.name }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone }}"}, {"name": "patient_email", "value": "={{ $json.patient.email }}"}, {"name": "appointment_date", "value": "={{ $json.appointment.date }}"}, {"name": "appointment_time", "value": "={{ $json.appointment.time }}"}, {"name": "doctor_name", "value": "={{ $json.appointment.doctor }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}]}, "options": {}}, "id": "extract-data", "name": "Extract Appointment Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"unit": "hours", "amount": 24}, "id": "wait-24h", "name": "Wait 24 Hours", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"unit": "hours", "amount": 2}, "id": "wait-2h", "name": "Wait 2 Hours Before", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"fromEmail": "noreply@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Appointment Reminder - {{ $json.practice_name }}", "text": "Dear {{ $json.patient_name }},\n\nThis is a friendly reminder that you have an appointment scheduled:\n\nDate: {{ $json.appointment_date }}\nTime: {{ $json.appointment_time }}\nDoctor: {{ $json.doctor_name }}\nLocation: {{ $json.practice_name }}\n\nPlease reply CONFIRM to confirm your appointment or CANC<PERSON> if you need to reschedule.\n\nIf you have any questions, please call us at {{ $json.practice_phone }}.\n\nThank you,\n{{ $json.practice_name }} Team"}, "id": "send-24h-email", "name": "Send 24h <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "Hi {{ $json.patient_name }}, you have an appointment tomorrow at {{ $json.appointment_time }} with Dr. {{ $json.doctor_name }} at {{ $json.practice_name }}. Reply CONFIRM to confirm or CANCEL to reschedule. Questions? Call {{ $json.practice_phone }}"}]}}, "id": "send-24h-sms", "name": "Send 24h SMS Reminder", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "URGENT: {{ $json.patient_name }}, your appointment with Dr. {{ $json.doctor_name }} is in 2 hours ({{ $json.appointment_time }}). Please confirm or call {{ $json.practice_phone }} immediately if you need to cancel."}]}}, "id": "send-2h-sms", "name": "Send 2h SMS Reminder", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Appointment reminder workflow initiated\",\n  \"patient\": \"{{ $json.patient_name }}\",\n  \"appointment\": \"{{ $json.appointment_date }} at {{ $json.appointment_time }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Appointment Scheduled Webhook": {"main": [[{"node": "Extract Appointment Data", "type": "main", "index": 0}]]}, "Extract Appointment Data": {"main": [[{"node": "Wait 24 Hours", "type": "main", "index": 0}, {"node": "Wait 2 Hours Before", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Wait 24 Hours": {"main": [[{"node": "Send 24h <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Send 24h SMS Reminder", "type": "main", "index": 0}]]}, "Wait 2 Hours Before": {"main": [[{"node": "Send 2h SMS Reminder", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "dental-appointment-reminders", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "healthcare", "name": "healthcare"}]}