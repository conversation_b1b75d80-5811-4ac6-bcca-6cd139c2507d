{"name": "Patient Follow-up & Retention System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "treatment-completed", "responseMode": "responseNode", "options": {}}, "id": "treatment-completed-webhook", "name": "Treatment Completed Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "treatment-completed"}, {"parameters": {"values": {"string": [{"name": "patient_name", "value": "={{ $json.patient.name }}"}, {"name": "patient_email", "value": "={{ $json.patient.email }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone }}"}, {"name": "treatment_type", "value": "={{ $json.treatment.type }}"}, {"name": "treatment_date", "value": "={{ $json.treatment.date }}"}, {"name": "doctor_name", "value": "={{ $json.treatment.doctor }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}, {"name": "next_recall_months", "value": "={{ $json.treatment.recall_period || 6 }}"}]}, "options": {}}, "id": "extract-treatment-data", "name": "Extract Treatment Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"unit": "hours", "amount": 2}, "id": "wait-2h-post-treatment", "name": "Wait 2 Hours Post-Treatment", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"fromEmail": "care@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Post-Treatment Care Instructions - {{ $json.practice_name }}", "text": "Dear {{ $json.patient_name }},\n\nThank you for visiting {{ $json.practice_name }} today for your {{ $json.treatment_type }}.\n\nPost-Treatment Care Instructions:\n\n🦷 For the next 24 hours:\n• Avoid hot foods and beverages\n• Take prescribed medications as directed\n• Rinse gently with warm salt water\n• Avoid hard or sticky foods\n\n⚠️ Contact us immediately if you experience:\n• Severe pain not relieved by medication\n• Excessive bleeding\n• Signs of infection (fever, swelling)\n• Any unusual symptoms\n\n📞 Emergency contact: {{ $json.practice_phone }}\n\nWe hope you're feeling better soon. If you have any questions or concerns, please don't hesitate to reach out.\n\nBest regards,\nDr. {{ $json.doctor_name }} and the {{ $json.practice_name }} team"}, "id": "post-treatment-care-email", "name": "Post-Treatment Care Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"unit": "days", "amount": 1}, "id": "wait-24h-followup", "name": "Wait 24h for Follow-up", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 350]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "Hi {{ $json.patient_name }}, this is {{ $json.practice_name }}. How are you feeling after your {{ $json.treatment_type }} yesterday? Any pain or concerns? Reply or call {{ $json.practice_phone }} if you need assistance."}]}}, "id": "followup-sms", "name": "24h Follow-up SMS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 350]}, {"parameters": {"unit": "days", "amount": 7}, "id": "wait-1-week", "name": "Wait 1 Week", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"fromEmail": "feedback@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "How was your experience? We'd love your feedback!", "text": "Dear {{ $json.patient_name }},\n\nWe hope you're feeling great after your recent {{ $json.treatment_type }} with Dr. {{ $json.doctor_name }}!\n\nYour feedback is incredibly valuable to us. Would you mind taking a moment to share your experience?\n\n⭐ Leave a Google Review: [GOOGLE_REVIEW_LINK]\n⭐ Leave a Yelp Review: [YELP_REVIEW_LINK]\n📝 Quick Feedback Survey: [SURVEY_LINK]\n\nYour reviews help other patients find quality dental care and help us continue improving our services.\n\nAs a thank you for your feedback, we'd like to offer you 10% off your next cleaning appointment.\n\nThank you for choosing {{ $json.practice_name }}!\n\nWarm regards,\nThe {{ $json.practice_name }} Team"}, "id": "review-request-email", "name": "Review Request Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"unit": "months", "amount": "={{ parseInt($json.next_recall_months) - 1 }}"}, "id": "wait-recall-reminder", "name": "Wait for <PERSON><PERSON><PERSON>er", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 650]}, {"parameters": {"fromEmail": "scheduling@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Time for Your Next Dental Cleaning - {{ $json.practice_name }}", "text": "Dear {{ $json.patient_name }},\n\nIt's time for your regular dental cleaning and check-up! It's been {{ $json.next_recall_months }} months since your last visit on {{ $json.treatment_date }}.\n\nRegular cleanings help:\n✓ Prevent cavities and gum disease\n✓ Detect problems early\n✓ Maintain your beautiful smile\n✓ Keep your overall health in check\n\n🗓️ Schedule online: [SCHEDULING_LINK]\n📞 Call us: {{ $json.practice_phone }}\n\nWe have convenient appointment times available:\n• Morning slots: 8:00 AM - 12:00 PM\n• Afternoon slots: 1:00 PM - 5:00 PM\n• Evening slots: 6:00 PM - 8:00 PM (select days)\n\nDon't wait - book your appointment today!\n\nLooking forward to seeing you soon,\nDr. {{ $json.doctor_name }} and the {{ $json.practice_name }} team"}, "id": "recall-appointment-email", "name": "Recall Appointment Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 650]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Patient follow-up sequence initiated\",\n  \"patient\": \"{{ $json.patient_name }}\",\n  \"treatment\": \"{{ $json.treatment_type }}\",\n  \"next_recall\": \"{{ $json.next_recall_months }} months\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Treatment Completed Webhook": {"main": [[{"node": "Extract Treatment Data", "type": "main", "index": 0}]]}, "Extract Treatment Data": {"main": [[{"node": "Wait 2 Hours Post-Treatment", "type": "main", "index": 0}, {"node": "Wait 24h for Follow-up", "type": "main", "index": 0}, {"node": "Wait 1 Week", "type": "main", "index": 0}, {"node": "Wait for <PERSON><PERSON><PERSON>er", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Wait 2 Hours Post-Treatment": {"main": [[{"node": "Post-Treatment Care Email", "type": "main", "index": 0}]]}, "Wait 24h for Follow-up": {"main": [[{"node": "24h Follow-up SMS", "type": "main", "index": 0}]]}, "Wait 1 Week": {"main": [[{"node": "Review Request Email", "type": "main", "index": 0}]]}, "Wait for Recall Reminder": {"main": [[{"node": "Recall Appointment Email", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "patient-followup-retention", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "retention", "name": "retention"}]}