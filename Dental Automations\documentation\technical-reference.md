# 🔧 Technical Reference Guide

## Webhook API Documentation

### 1. Appointment Reminder System

**Endpoint:** `/webhook/appointment-scheduled`
**Method:** POST
**Content-Type:** application/json

**Request Payload:**
```json
{
  "patient": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "appointment": {
    "date": "2025-01-15",
    "time": "10:00 AM",
    "doctor": "<PERSON><PERSON>",
    "type": "Cleaning"
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********",
    "address": "123 Main St, City, State 12345"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Appointment reminder workflow initiated",
  "patient": "<PERSON>",
  "appointment": "2025-01-15 at 10:00 AM"
}
```

### 2. New Patient Onboarding

**Endpoint:** `/webhook/new-patient`
**Method:** POST

**Request Payload:**
```json
{
  "patient": {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+**********",
    "date_of_birth": "1985-06-15",
    "insurance": "Delta Dental"
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********",
    "address": "123 Main St, City, State 12345",
    "primary_doctor": "Dr. Johnson"
  }
}
```

### 3. Patient Follow-up & Retention

**Endpoint:** `/webhook/treatment-completed`
**Method:** POST

**Request Payload:**
```json
{
  "patient": {
    "name": "Bob Wilson",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "treatment": {
    "type": "Root Canal",
    "date": "2025-01-10",
    "doctor": "Dr. Smith",
    "recall_period": 6
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********"
  }
}
```

### 4. Emergency & Urgent Care

**Endpoint:** `/webhook/emergency-request`
**Method:** POST

**Request Payload:**
```json
{
  "patient": {
    "name": "Emergency Patient",
    "email": "<EMAIL>",
    "phone": "+**********",
    "is_existing": true
  },
  "emergency": {
    "type": "Severe Toothache",
    "pain_level": 9,
    "description": "Severe pain in upper left molar, started 2 hours ago"
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********",
    "on_call_doctor": "Dr. Emergency",
    "on_call_phone": "+**********"
  }
}
```

### 5. Administrative Automation

**Endpoint:** `/webhook/admin-trigger`
**Method:** POST

**Request Payload:**
```json
{
  "trigger": {
    "type": "payment_reminder"
  },
  "patient": {
    "name": "Patient Name",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "billing": {
    "amount_due": "150.00",
    "due_date": "2025-01-20",
    "invoice_number": "INV-2025-001"
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********"
  }
}
```

**Trigger Types:**
- `payment_reminder`
- `insurance_update`
- `staff_notification`
- `inventory_alert`

### 6. Marketing & Growth

**Endpoint:** `/webhook/marketing-trigger`
**Method:** POST

**Request Payload:**
```json
{
  "campaign": {
    "type": "referral_reward"
  },
  "patient": {
    "name": "Referring Patient",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "referral": {
    "referrer_name": "John Doe",
    "referrer_email": "<EMAIL>"
  },
  "practice": {
    "name": "Smile Dental Practice",
    "phone": "+**********",
    "website": "https://smiledental.com"
  }
}
```

**Campaign Types:**
- `referral_reward`
- `cosmetic_promotion`
- `patient_reactivation`
- `birthday_greeting`
- `newsletter`

## Node Configuration Reference

### Email Node Configuration

**SMTP Settings:**
```json
{
  "host": "smtp.gmail.com",
  "port": 587,
  "secure": false,
  "auth": {
    "user": "<EMAIL>",
    "pass": "your-app-password"
  }
}
```

**Email Template Variables:**
- `{{ $json.patient_name }}` - Patient's full name
- `{{ $json.practice_name }}` - Practice name
- `{{ $json.appointment_date }}` - Appointment date
- `{{ $json.appointment_time }}` - Appointment time
- `{{ $json.doctor_name }}` - Doctor's name
- `{{ $json.practice_phone }}` - Practice phone number

### SMS Node Configuration

**Twilio Settings:**
```json
{
  "accountSid": "ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "authToken": "your-auth-token",
  "phoneNumber": "+**********"
}
```

**SMS Template Variables:**
- Character limit: 160 characters
- Use same variables as email templates
- Include opt-out instructions

### Wait Node Configuration

**Time Units:**
- `minutes` - For short delays
- `hours` - For same-day timing
- `days` - For multi-day sequences
- `months` - For long-term follow-ups

**Examples:**
```json
{
  "unit": "hours",
  "amount": 24
}
```

## Database Schema Requirements

### Patient Data Structure
```sql
CREATE TABLE patients (
  id INT PRIMARY KEY,
  name VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(20),
  date_of_birth DATE,
  insurance VARCHAR(255),
  last_visit DATE,
  is_active BOOLEAN
);
```

### Appointment Data Structure
```sql
CREATE TABLE appointments (
  id INT PRIMARY KEY,
  patient_id INT,
  doctor_id INT,
  appointment_date DATE,
  appointment_time TIME,
  type VARCHAR(100),
  status VARCHAR(50),
  created_at TIMESTAMP
);
```

### Treatment Data Structure
```sql
CREATE TABLE treatments (
  id INT PRIMARY KEY,
  patient_id INT,
  appointment_id INT,
  treatment_type VARCHAR(255),
  completed_date DATE,
  doctor_id INT,
  recall_period INT,
  notes TEXT
);
```

## Integration Examples

### Open Dental Integration
```php
// PHP webhook trigger example
$webhook_url = "https://your-n8n-instance.com/webhook/appointment-scheduled";
$data = [
    "patient" => [
        "name" => $patient->getName(),
        "email" => $patient->getEmail(),
        "phone" => $patient->getPhone()
    ],
    "appointment" => [
        "date" => $appointment->getDate(),
        "time" => $appointment->getTime(),
        "doctor" => $appointment->getDoctor()
    ],
    "practice" => [
        "name" => "Your Practice Name",
        "phone" => "+**********"
    ]
];

$ch = curl_init($webhook_url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_exec($ch);
curl_close($ch);
```

### Dentrix Integration
```csharp
// C# webhook trigger example
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;

public async Task TriggerWebhook(Patient patient, Appointment appointment)
{
    var webhookUrl = "https://your-n8n-instance.com/webhook/appointment-scheduled";
    var data = new
    {
        patient = new
        {
            name = patient.Name,
            email = patient.Email,
            phone = patient.Phone
        },
        appointment = new
        {
            date = appointment.Date.ToString("yyyy-MM-dd"),
            time = appointment.Time.ToString("HH:mm"),
            doctor = appointment.Doctor
        },
        practice = new
        {
            name = "Your Practice Name",
            phone = "+**********"
        }
    };

    using var client = new HttpClient();
    var json = JsonConvert.SerializeObject(data);
    var content = new StringContent(json, Encoding.UTF8, "application/json");
    await client.PostAsync(webhookUrl, content);
}
```

## Error Handling

### Common Error Codes
- `400` - Bad Request (invalid JSON)
- `401` - Unauthorized (invalid credentials)
- `404` - Not Found (invalid webhook URL)
- `429` - Rate Limited (too many requests)
- `500` - Internal Server Error

### Retry Logic
```javascript
// JavaScript retry example
async function sendWebhook(url, data, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
            
            if (response.ok) return response;
            
            if (i === maxRetries - 1) throw new Error('Max retries reached');
            
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        } catch (error) {
            if (i === maxRetries - 1) throw error;
        }
    }
}
```

## Performance Optimization

### Webhook Best Practices
1. **Async Processing:** Don't wait for webhook response
2. **Batch Operations:** Group multiple events when possible
3. **Rate Limiting:** Respect API limits
4. **Error Handling:** Implement proper retry logic
5. **Monitoring:** Track webhook success rates

### n8n Performance Tips
1. **Resource Allocation:** Ensure adequate server resources
2. **Workflow Optimization:** Minimize unnecessary nodes
3. **Credential Management:** Use secure credential storage
4. **Monitoring:** Set up workflow monitoring
5. **Scaling:** Consider n8n cloud for high volume

## Security Considerations

### Webhook Security
- Use HTTPS endpoints only
- Implement webhook signature verification
- Validate all input data
- Use rate limiting
- Monitor for suspicious activity

### Data Protection
- Encrypt sensitive data in transit
- Use secure credential storage
- Implement access controls
- Regular security audits
- HIPAA compliance measures

---

**For additional technical support or custom integrations, please contact our development team.**
