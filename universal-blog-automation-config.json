{
  "business_profile": {
    "business_name": "{{BUSINESS_NAME}}",
    "industry": "{{INDUSTRY}}",
    "business_type": "{{BUSINESS_TYPE}}", // local, national, international, e-commerce, service, product
    "location": {
      "city": "{{CITY}}",
      "state": "{{STATE}}",
      "country": "{{COUNTRY}}",
      "service_areas": ["{{SERVICE_AREA_1}}", "{{SERVICE_AREA_2}}"]
    },
    "target_audience": {
      "demographics": "{{TARGET_DEMOGRAPHICS}}",
      "age_range": "{{AGE_RANGE}}",
      "income_level": "{{INCOME_LEVEL}}",
      "pain_points": ["{{PAIN_POINT_1}}", "{{PAIN_POINT_2}}", "{{PAIN_POINT_3}}"],
      "search_behavior": "{{SEARCH_BEHAVIOR}}"
    },
    "services_products": [
      {
        "name": "{{SERVICE_1}}",
        "category": "{{CATEGORY_1}}",
        "keywords": ["{{KEYWORD_1}}", "{{KEYWORD_2}}"],
        "commercial_intent": "high|medium|low"
      }
    ],
    "brand_voice": {
      "tone": "{{BRAND_TONE}}", // professional, friendly, authoritative, casual
      "personality": "{{BRAND_PERSONALITY}}",
      "expertise_level": "{{EXPERTISE_LEVEL}}" // beginner, intermediate, expert
    },
    "competitors": ["{{COMPETITOR_1}}", "{{COMPETITOR_2}}"],
    "unique_selling_points": ["{{USP_1}}", "{{USP_2}}"]
  },
  
  "seo_strategy": {
    "primary_keywords": [
      {
        "keyword": "{{PRIMARY_KEYWORD_1}}",
        "search_volume": "{{SEARCH_VOLUME}}",
        "difficulty": "{{DIFFICULTY}}",
        "intent": "informational|commercial|transactional|navigational"
      }
    ],
    "long_tail_keywords": ["{{LONG_TAIL_1}}", "{{LONG_TAIL_2}}"],
    "local_seo_terms": ["{{LOCAL_TERM_1}}", "{{LOCAL_TERM_2}}"],
    "semantic_keywords": ["{{SEMANTIC_1}}", "{{SEMANTIC_2}}"],
    "content_pillars": [
      {
        "pillar": "{{CONTENT_PILLAR_1}}",
        "subtopics": ["{{SUBTOPIC_1}}", "{{SUBTOPIC_2}}"],
        "target_keywords": ["{{PILLAR_KEYWORD_1}}", "{{PILLAR_KEYWORD_2}}"]
      }
    ]
  },
  
  "content_strategy": {
    "content_types": ["how-to", "listicle", "case-study", "comparison", "news", "opinion"],
    "content_length": {
      "min_words": 1500,
      "max_words": 3000,
      "optimal_range": "2000-2500"
    },
    "publishing_frequency": "{{FREQUENCY}}", // daily, weekly, bi-weekly
    "content_mix": {
      "educational": 40,
      "commercial": 30,
      "brand_awareness": 20,
      "trending": 10
    },
    "seasonal_topics": [
      {
        "season": "{{SEASON}}",
        "topics": ["{{SEASONAL_TOPIC_1}}", "{{SEASONAL_TOPIC_2}}"]
      }
    ]
  },
  
  "ai_content_humanization": {
    "writing_style": {
      "sentence_variety": true,
      "paragraph_length": "varied", // short, medium, long, varied
      "use_contractions": true,
      "personal_pronouns": true,
      "rhetorical_questions": true,
      "transitional_phrases": true
    },
    "human_elements": {
      "personal_anecdotes": true,
      "industry_specific_jargon": true,
      "local_references": true,
      "current_events": true,
      "expert_quotes": true,
      "statistics_citations": true
    },
    "content_structure": {
      "hook_types": ["question", "statistic", "story", "controversy"],
      "subheading_style": "benefit-driven",
      "conclusion_cta": true,
      "internal_linking": true,
      "external_authority_links": true
    }
  },
  
  "technical_seo": {
    "meta_optimization": {
      "title_template": "{{KEYWORD}} | {{BUSINESS_NAME}} - {{LOCATION}}",
      "description_template": "{{BENEFIT}} {{KEYWORD}} in {{LOCATION}}. {{BUSINESS_NAME}} offers {{SERVICE}}. {{CTA}}",
      "max_title_length": 60,
      "max_description_length": 160
    },
    "schema_markup": {
      "organization": true,
      "local_business": true,
      "article": true,
      "faq": true,
      "how_to": true,
      "review": true
    },
    "image_optimization": {
      "alt_text_template": "{{KEYWORD}} - {{BUSINESS_NAME}} {{LOCATION}}",
      "file_naming": "{{keyword}}-{{business}}-{{location}}.jpg",
      "compression": true,
      "webp_format": true
    }
  },
  
  "content_distribution": {
    "primary_platform": "wordpress",
    "social_media": {
      "facebook": true,
      "linkedin": true,
      "twitter": true,
      "instagram": false
    },
    "email_marketing": true,
    "guest_posting": true,
    "content_syndication": false
  },
  
  "performance_tracking": {
    "kpis": [
      "organic_traffic",
      "keyword_rankings",
      "conversion_rate",
      "bounce_rate",
      "time_on_page",
      "backlinks",
      "local_pack_rankings"
    ],
    "tools": {
      "google_analytics": true,
      "google_search_console": true,
      "rank_tracking": true,
      "backlink_monitoring": true
    },
    "reporting_frequency": "weekly"
  },
  
  "compliance_safety": {
    "fact_checking": true,
    "plagiarism_detection": true,
    "ai_detection_avoidance": {
      "content_rewriting": true,
      "human_editing": true,
      "style_variation": true,
      "source_attribution": true
    },
    "legal_disclaimers": true,
    "privacy_compliance": true
  }
}
