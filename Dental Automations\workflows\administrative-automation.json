{"name": "Administrative Automation System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "admin-trigger", "responseMode": "responseNode", "options": {}}, "id": "admin-webhook", "name": "Administrative Trigger <PERSON>hook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "admin-trigger"}, {"parameters": {"values": {"string": [{"name": "trigger_type", "value": "={{ $json.trigger.type }}"}, {"name": "patient_name", "value": "={{ $json.patient.name || '' }}"}, {"name": "patient_email", "value": "={{ $json.patient.email || '' }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone || '' }}"}, {"name": "amount_due", "value": "={{ $json.billing.amount_due || '' }}"}, {"name": "due_date", "value": "={{ $json.billing.due_date || '' }}"}, {"name": "invoice_number", "value": "={{ $json.billing.invoice_number || '' }}"}, {"name": "insurance_claim_id", "value": "={{ $json.insurance.claim_id || '' }}"}, {"name": "claim_status", "value": "={{ $json.insurance.status || '' }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}]}, "options": {}}, "id": "extract-admin-data", "name": "Extract Administrative Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.trigger_type }}", "value2": "payment_reminder"}]}}, "id": "check-trigger-type", "name": "Check Trigger Type", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"fromEmail": "billing@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Payment Reminder - Invoice #{{ $json.invoice_number }}", "text": "Dear {{ $json.patient_name }},\n\nThis is a friendly reminder that you have an outstanding balance with {{ $json.practice_name }}.\n\n💰 Amount Due: ${{ $json.amount_due }}\n📅 Due Date: {{ $json.due_date }}\n📄 Invoice Number: {{ $json.invoice_number }}\n\nPayment Options:\n🌐 Online: [PAYMENT_PORTAL_LINK]\n📞 Phone: {{ $json.practice_phone }}\n🏢 In-Person: Visit our office during business hours\n💳 Accepted: Cash, Check, Credit/Debit Cards\n\nIf you have any questions about your bill or need to set up a payment plan, please don't hesitate to contact us.\n\nThank you for choosing {{ $json.practice_name }}!\n\nBest regards,\n{{ $json.practice_name }} Billing Department"}, "id": "payment-reminder-email", "name": "Payment Reminder Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"fromEmail": "insurance@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "office@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "subject": "Insurance Claim Status Update - {{ $json.insurance_claim_id }}", "text": "Insurance Claim Status Update\n\n📋 Claim ID: {{ $json.insurance_claim_id }}\n📊 Status: {{ $json.claim_status }}\n👤 Patient: {{ $json.patient_name }}\n📅 Date: {{ new Date().toLocaleDateString() }}\n\nAction Required:\n{{ $json.claim_status === 'denied' ? '❌ Claim has been denied. Review and resubmit if necessary.' : '' }}\n{{ $json.claim_status === 'pending' ? '⏳ Claim is still pending. Follow up may be required.' : '' }}\n{{ $json.claim_status === 'approved' ? '✅ Claim has been approved. Payment should be received soon.' : '' }}\n{{ $json.claim_status === 'partial' ? '⚠️ Claim partially approved. Review coverage details.' : '' }}\n\nPlease review and take appropriate action.\n\n{{ $json.practice_name }} Administrative System"}, "id": "insurance-status-email", "name": "Insurance Status Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "Hi {{ $json.patient_name }}, this is {{ $json.practice_name }}. You have an outstanding balance of ${{ $json.amount_due }} due {{ $json.due_date }}. Pay online or call {{ $json.practice_phone }}. Thank you!"}]}}, "id": "payment-reminder-sms", "name": "Payment Reminder SMS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"fromEmail": "staff@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "staff@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "subject": "Staff Schedule Notification - {{ new Date().toLocaleDateString() }}", "text": "Daily Staff Schedule Notification\n\n📅 Date: {{ new Date().toLocaleDateString() }}\n🏥 Practice: {{ $json.practice_name }}\n\nSchedule Updates:\n• Check today's appointments and staffing levels\n• Review any last-minute changes or cancellations\n• Ensure adequate coverage for all shifts\n• Confirm on-call arrangements\n\nReminders:\n✓ Update patient charts\n✓ Verify insurance information\n✓ Prepare treatment rooms\n✓ Check inventory levels\n\nFor schedule changes, contact the office manager immediately.\n\n{{ $json.practice_name }} Administrative System"}, "id": "staff-notification-email", "name": "Staff Schedule Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"fromEmail": "inventory@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "manager@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "subject": "Low Inventory Alert - {{ $json.practice_name }}", "text": "⚠️ LOW INVENTORY ALERT ⚠️\n\n📦 Practice: {{ $json.practice_name }}\n📅 Date: {{ new Date().toLocaleDateString() }}\n\nItems requiring attention:\n• Dental supplies running low\n• Office supplies need restocking\n• Equipment maintenance due\n• Medication inventory check needed\n\nRecommended Actions:\n1. Review current inventory levels\n2. Place orders for critical supplies\n3. Schedule equipment maintenance\n4. Update inventory tracking system\n\nPlease address these items promptly to ensure uninterrupted patient care.\n\n{{ $json.practice_name }} Inventory Management System"}, "id": "inventory-alert-email", "name": "Inventory <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 600]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Administrative automation triggered\",\n  \"trigger_type\": \"{{ $json.trigger_type }}\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Administrative Trigger Webhook": {"main": [[{"node": "Extract Administrative Data", "type": "main", "index": 0}]]}, "Extract Administrative Data": {"main": [[{"node": "Check Trigger Type", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Check Trigger Type": {"main": [[{"node": "Payment Reminder Email", "type": "main", "index": 0}, {"node": "Payment Reminder SMS", "type": "main", "index": 0}], [{"node": "Insurance Status Email", "type": "main", "index": 0}], [{"node": "Staff Schedule Notification", "type": "main", "index": 0}], [{"node": "Inventory <PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "administrative-automation", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "administration", "name": "administration"}]}