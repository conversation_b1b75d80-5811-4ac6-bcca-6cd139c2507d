{"nodes": [{"parameters": {"jsonSchemaExample": "{\n    \"title\": \"title\",\n    \"content\": \"content\"\n}"}, "id": "cd82b72d-8bab-4c92-ab98-d72e092ba046", "name": "Structured Output - JSON", "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "position": [-916, 210], "typeVersion": 1.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('AI Agent1').item.json.output.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.output }}", "rightValue": ""}], "combinator": "and"}, "options": {}}, "id": "3aed8de7-5584-4147-8252-fcc992a21fbc", "name": "If1", "type": "n8n-nodes-base.if", "position": [128, 40], "typeVersion": 2.2}, {"parameters": {"operation": "createFromText", "content": "={{ $json.output.content }}", "name": "={{ $json.output.title }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1wkQBxQxvznPjamIA_yqYFkTtRw0xNf4t", "mode": "list", "cachedResultName": "BLOGS", "cachedResultUrl": "https://drive.google.com/drive/folders/1wkQBxQxvznPjamIA_yqYFkTtRw0xNf4t"}, "options": {}}, "id": "6d6db83e-f54a-4fe6-a1b1-d0e98e58d7b1", "name": "Google Drive", "type": "n8n-nodes-base.googleDrive", "position": [-170, -260], "typeVersion": 3, "credentials": {"googleDriveOAuth2Api": {"id": "GicvtgMWua71Ctkt", "name": "Google Drive account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "3e8d2523-66aa-46fe-adcc-39dc78b9161e", "name": "topic", "type": "string", "value": "={{ $json.output }}"}]}, "options": {}}, "id": "ec79ada5-ed26-4abb-893a-1d876452bd86", "name": "Set Blog Topic", "type": "n8n-nodes-base.set", "position": [-1284, -10], "typeVersion": 3.4}, {"parameters": {"html": "={{ $json.output.content }}", "options": {}}, "id": "5b6386ba-8770-4bf6-bc15-da388aed9cad", "name": "HTML to Markdown", "type": "n8n-nodes-base.markdown", "position": [-468, -110], "typeVersion": 1}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "conditions": [{"id": "aaf83c73-65f3-4a88-87f3-25b1acaf93ef", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('AI Agent1').item.json.output.title }}", "rightValue": ""}, {"id": "d9af5bce-f0fb-4c20-8b6a-b01a3bf3e1d1", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $('AI Agent1').item.json.output.content }}", "rightValue": ""}], "combinator": "and"}, "options": {}}, "id": "fb4fcb92-3185-4cfc-96f1-085adb304d89", "name": "Tiltle & Content Exist?", "type": "n8n-nodes-base.if", "position": [-688, -10], "typeVersion": 2.2}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=Rewrite this article at a grade 9 reading level using appropriate metaphors.  Ensure you retain all original content and only use the provided original content for the rewriting.  Do not create a Title.\n\nProvide final response in html format following these guidelines:\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\n\n## Original content:  {{ $json.data }}", "options": {}}, "id": "a70e950e-29d7-427d-81a6-12d9f850e743", "name": "Rewrite for Grade 9 Reading Level1", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-248, 40], "typeVersion": 1.6}, {"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-2100, -10], "id": "b0f776c4-**************-b68c1fadbbe6", "name": "Schedule Trigger"}, {"parameters": {"documentId": {"__rl": true, "value": "1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o", "mode": "list", "cachedResultName": "VM - Master Sheet", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "AB - Blog Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o/edit#gid=**********"}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-1880, -10], "id": "81b70987-54a4-4c7b-a95b-3273ffa60540", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "pWWGze3qtLgpVzov", "name": "<PERSON><PERSON>"}}}, {"parameters": {"promptType": "define", "text": "=You are an expert local SEO content strategist for Raga Dental in Thanjavur.\n\nClient Info:\n- Name: Raga Dental Thanjavur\n- Industry: Healthcare/Dental\n- Audience: Adults in Thanjavur, age 25-60, seeking dental care\n- Demographics: Middle to upper-income, Tamil-speaking, urban and semi-urban residents\n- Pain Points: High cost of implants, fear of pain, lack of trust in dental procedures, inconvenience of appointments\n- Brand Voice: Professional, caring, trustworthy, approachable\n- Specialization: Dental implants and laser treatments\n\nExisting blog topics to AVOID:\n{{$json[\"topic\"]}}.join(\", \")\n\nGenerate a NEW blog topic that:\n1. Is unique and not similar to existing topics\n2. Targets local Thanjavur dental searches\n3. Addresses specific pain points\n4. Has high commercial intent\n\nReturn a JSON object with:\n{\n  \"topic\": \"[New unique topic]\",\n  \"category\": \"[dental-implants/laser-dentistry/cosmetic/general]\",\n  \"search_volume\": \"[estimated monthly searches]\",\n  \"keyword_difficulty\": \"[low/medium/high]\"\n}\n\nAnd give the final output as the perfect blog title.", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1660, -10], "id": "2d34ec91-dcfa-4884-ad6e-7292d6a59d8b", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1572, 210], "id": "7f53980f-c764-431c-b07c-e829b219199e", "name": "OpenAI Chat Model3", "credentials": {"openAiApi": {"id": "kW09KvdRrOSrrYlj", "name": "Primary"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1036, 210], "id": "6be2cee2-9d29-428d-9884-3e1fdc410eba", "name": "OpenAI Chat Model4", "credentials": {"openAiApi": {"id": "kW09KvdRrOSrrYlj", "name": "Primary"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08KCFN29AN", "mode": "list", "cachedResultName": "all-viral-magicians"}, "text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [-468, 90], "id": "7e33eee5-8e26-4462-a127-fe609c690bf1", "name": "<PERSON><PERSON>ck", "webhookId": "6fbc5b7f-196f-46fd-91df-6084188f18a4", "credentials": {"slackApi": {"id": "u9ld7unietz99J4F", "name": "Slack Bot Token"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "C08KCFN29AN", "mode": "list", "cachedResultName": "all-viral-magicians"}, "text": "=Error!  Title or Content Missing.  Workflow aborted at {{ $now }}", "otherOptions": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [348, -60], "id": "bec8f3bf-784b-4002-9229-d4c9b944e5ad", "name": "Slack2", "webhookId": "6fbc5b7f-196f-46fd-91df-6084188f18a4", "credentials": {"slackApi": {"id": "u9ld7unietz99J4F", "name": "Slack Bot Token"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-160, 260], "id": "4342fab7-3528-4eee-b1ec-e3be6b04c36d", "name": "OpenAI Chat Model5", "credentials": {"openAiApi": {"id": "kW09KvdRrOSrrYlj", "name": "Primary"}}}, {"parameters": {"operation": "sendAndWait", "select": "channel", "channelId": {"__rl": true, "value": "C08KCFN29AN", "mode": "list", "cachedResultName": "all-viral-magicians"}, "message": "=Success! Your blog post was created at {{ $now }}and this is the link {{ $json.link }}", "options": {}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1448, 140], "id": "5f9b5588-5f61-4437-8802-795441a86d19", "name": "Slack3", "webhookId": "059e2842-8492-4170-8052-93e3f87555d2", "credentials": {"slackApi": {"id": "u9ld7unietz99J4F", "name": "Slack Bot Token"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.topic }} Analyze the provided article topic and create a compelling blog post that will be returned in JSON format with two fields: \"title\" and \"content\". Follow these specifications:\n\n## Title Requirements\n- Create an engaging, SEO-friendly title under 10 words\n- Must not contain a colon\n- Should capture the article's essence\n- Will be formatted as an H1 in the content\n\n## Content Structure\n- Introduction (150-200 words)\n  * Compelling hook\n  * Topic context and importance\n  * Preview of main points\n\n- Main Content (6-8 chapters)\n  * Each chapter requires:\n    - Relevant H2 heading\n    - 300-400 words of unique content\n    - Specific topic focus\n    - Source material quotes/data\n    - Smooth transitions\n\n- Conclusion (200-250 words)\n  * Key takeaways\n  * Final thoughts/implications\n\n## Formatting Guidelines\n- Use proper HTML tags throughout\n- Limit yourself to bold, italics, paragraphs and lists\n- Structure with <p> tags for paragraphs\n- Include appropriate spacing\n- Use <blockquote> for direct quotes\n- Maintain consistent formatting\n- Write in clear, professional tone\n- Break up long paragraphs\n- Use engaging subheadings\n- Include transitional phrases\n\nThe content should be original, avoid direct copying, and maintain a consistent voice throughout. The final JSON response should contain only the title and content fields, with the content including all HTML formatting.", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1064, -10], "id": "e145d995-0174-4b88-9c20-ba385a7217c7", "name": "AI Agent1"}, {"parameters": {"operation": "update", "postId": "post-id", "updateFields": {"status": "publish"}}, "type": "n8n-nodes-base.wordpress", "typeVersion": 1, "position": [1668, 140], "id": "9caa3748-b521-4f49-934d-fbb29b706344", "name": "Wordpress", "credentials": {"wordpressApi": {"id": "hE73ytYDXGr05bPq", "name": "RAGA DENTAL"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o", "mode": "list", "cachedResultName": "VM - Master Sheet", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "AB - Blog Topics", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1keF--FrLmCInqXNBwa_MiQv75Xm6ql0E2hlMDW-Hs3o/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [{"id": "topic", "displayName": "topic", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "post_url", "displayName": "post_url", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "category", "displayName": "category", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "predicted_traffic", "displayName": "predicted_traffic", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1888, 140], "id": "fd848de7-a628-4fcc-a74a-cb19e3081a2f", "name": "Google Sheets1", "credentials": {"googleSheetsOAuth2Api": {"id": "pWWGze3qtLgpVzov", "name": "<PERSON><PERSON>"}}}, {"parameters": {"method": "POST", "url": "=https://router.huggingface.co/hf-inference/models/black-forest-labs/FLUX.1-dev", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer *************************************"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "image/png"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={ \n  \"inputs\": \"{{ $('HTML to Markdown').item.json.output.title }} - High-quality image for a dental blog in Thanjavur. Clean and professional, suitable for WordPress featured image. No text or logos.\"\n}", "options": {}}, "name": "Generate Image", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [340, 140], "id": "f14b4173-76c9-4e39-9d7e-c8d547e97181"}, {"parameters": {"functionCode": "items[0].binary = {\n  data: {\n    fileName: 'featured.jpg',\n    mimeType: 'image/jpeg'\n  }\n};\nreturn items;"}, "name": "Prepare Image Upload", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [568, 140], "id": "93193a92-b43a-411c-8a7a-70622b0c9924"}, {"parameters": {"method": "POST", "url": "https://ragadental.com/wp-json/wp/v2/media", "authentication": "predefinedCredentialType", "nodeCredentialType": "wordpressApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Disposition", "value": "attachment; filename=featured.jpg"}, {"name": "Content-Type", "value": "image/jpeg"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "name": "Upload Featured Image", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [780, 140], "id": "5d1b376a-fee6-426a-ac51-449e712c61b0", "credentials": {"wordpressApi": {"id": "hE73ytYDXGr05bPq", "name": "RAGA DENTAL"}}}, {"parameters": {"method": "PUT", "url": "={{ 'https://ragadental.com/wp-json/wp/v2/posts/' + $json.id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "wordpressApi", "sendBody": true, "bodyParameters": {"parameters": [{"name": "featured_media", "value": "={{ $json.id }}"}, {"name": "status", "value": "publish"}]}, "options": {}}, "name": "Set Featured Image1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [1228, 140], "id": "9169c36a-643b-454b-b353-be33b566eef3", "credentials": {"wordpressApi": {"id": "hE73ytYDXGr05bPq", "name": "RAGA DENTAL"}}}, {"parameters": {"title": "={{ $('HTML to Markdown').item.json.output.title }}", "additionalFields": {"content": "={{ $json.content.replace('IMAGE_PLACEHOLDER', 'https://ragadental.com/wp-content/uploads/featured.jpg') }}", "status": "draft"}}, "name": "Create WordPress Post", "type": "n8n-nodes-base.wordpress", "typeVersion": 1, "position": [1000, 140], "id": "0c1707e3-0519-4aa7-a623-dec993416ca1", "credentials": {"wordpressApi": {"id": "hE73ytYDXGr05bPq", "name": "RAGA DENTAL"}}}], "connections": {"Structured Output - JSON": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "If1": {"main": [[{"node": "Generate Image", "type": "main", "index": 0}], [{"node": "Slack2", "type": "main", "index": 0}]]}, "Set Blog Topic": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "HTML to Markdown": {"main": [[{"node": "Rewrite for Grade 9 Reading Level1", "type": "main", "index": 0}, {"node": "Google Drive", "type": "main", "index": 0}]]}, "Tiltle & Content Exist?": {"main": [[{"node": "HTML to Markdown", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Rewrite for Grade 9 Reading Level1": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Set Blog Topic", "type": "main", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model5": {"ai_languageModel": [[{"node": "Rewrite for Grade 9 Reading Level1", "type": "ai_languageModel", "index": 0}]]}, "Slack3": {"main": [[{"node": "Wordpress", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Tiltle & Content Exist?", "type": "main", "index": 0}]]}, "Wordpress": {"main": [[{"node": "Google Sheets1", "type": "main", "index": 0}]]}, "Generate Image": {"main": [[{"node": "Prepare Image Upload", "type": "main", "index": 0}]]}, "Prepare Image Upload": {"main": [[{"node": "Upload Featured Image", "type": "main", "index": 0}]]}, "Upload Featured Image": {"main": [[{"node": "Create WordPress Post", "type": "main", "index": 0}]]}, "Set Featured Image1": {"main": [[{"node": "Slack3", "type": "main", "index": 0}]]}, "Create WordPress Post": {"main": [[{"node": "Set Featured Image1", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "f90926f5067e4325ab81139a111e506b0c1cde8f3b19b38a6342d0f76113e97c"}}