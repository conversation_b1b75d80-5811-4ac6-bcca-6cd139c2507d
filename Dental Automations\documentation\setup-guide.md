# 🛠️ Dental Automation Setup Guide

## Prerequisites Checklist

Before starting, ensure you have:

- [ ] n8n instance running (version 1.0+ recommended)
- [ ] Admin access to your n8n instance
- [ ] Email service credentials (SMTP or API)
- [ ] SMS service account (Twi<PERSON> recommended)
- [ ] Practice management system with webhook capability
- [ ] Basic understanding of n8n interface

## Step-by-Step Installation

### 1. Import Workflows

#### Method A: Individual Import
1. Open n8n dashboard
2. Click **"Workflows"** in the sidebar
3. Click **"Import from File"** button
4. Select the first workflow JSON file
5. Click **"Import"**
6. Repeat for all 6 workflow files

#### Method B: Bulk Import (if supported)
1. Select all 6 JSON files at once
2. Import them simultaneously
3. Verify all workflows appear in your list

### 2. Configure Credentials

#### Email Service Setup
1. Go to **Settings** → **Credentials**
2. Click **"Add Credential"**
3. Choose your email service:

**For Gmail:**
```
Type: Gmail
Email: <EMAIL>
Password: your-app-password
```

**For SMTP:**
```
Type: SMTP
Host: your-smtp-server.com
Port: 587 (or 465 for SSL)
Username: <EMAIL>
Password: your-password
Secure: true
```

#### Twilio SMS Setup
1. Create Twilio account at twilio.com
2. Get Account SID and Auth Token
3. Purchase a phone number
4. In n8n, add Twilio credential:
```
Account SID: ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Auth Token: your-auth-token
Phone Number: +**********
```

### 3. Workflow Configuration

#### Appointment Reminder System
1. Open the workflow
2. Click on **"Extract Appointment Data"** node
3. Update practice information:
   - Practice name
   - Practice phone
   - Practice address
4. Modify email templates in **"Send 24h Email Reminder"**
5. Adjust timing in **"Wait"** nodes if needed

#### New Patient Onboarding
1. Update welcome email template
2. Add your intake form links
3. Set practice-specific information
4. Configure scheduling links

#### Patient Follow-up & Retention
1. Customize post-treatment care instructions
2. Update review platform links (Google, Yelp)
3. Set recall periods (default: 6 months)
4. Modify follow-up messaging

#### Emergency & Urgent Care
1. Set on-call doctor information
2. Configure emergency contact numbers
3. Update pain management instructions
4. Set severity thresholds

#### Administrative Automation
1. Configure billing email templates
2. Set payment portal links
3. Update insurance claim tracking
4. Customize staff notification preferences

#### Marketing & Growth
1. Set referral reward amounts
2. Update cosmetic service offerings
3. Configure promotion schedules
4. Customize newsletter content

### 4. Webhook Configuration

Each workflow needs a unique webhook URL:

1. **Appointment Reminders:**
   ```
   URL: https://your-n8n-instance.com/webhook/appointment-scheduled
   Method: POST
   ```

2. **New Patient Onboarding:**
   ```
   URL: https://your-n8n-instance.com/webhook/new-patient
   Method: POST
   ```

3. **Patient Follow-up:**
   ```
   URL: https://your-n8n-instance.com/webhook/treatment-completed
   Method: POST
   ```

4. **Emergency Care:**
   ```
   URL: https://your-n8n-instance.com/webhook/emergency-request
   Method: POST
   ```

5. **Administrative:**
   ```
   URL: https://your-n8n-instance.com/webhook/admin-trigger
   Method: POST
   ```

6. **Marketing:**
   ```
   URL: https://your-n8n-instance.com/webhook/marketing-trigger
   Method: POST
   ```

### 5. Practice Management System Integration

#### For Open Dental:
1. Go to Setup → Automation
2. Add webhook endpoints for:
   - Appointment scheduled
   - New patient created
   - Treatment completed
3. Configure JSON payload format

#### For Dentrix:
1. Access Dentrix Enterprise settings
2. Configure API webhooks
3. Set up event triggers
4. Test webhook delivery

#### For Custom Integration:
Create webhook calls in your system that send POST requests with this JSON structure:

```json
{
  "patient": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "appointment": {
    "date": "2025-01-15",
    "time": "10:00 AM",
    "doctor": "Dr. Smith"
  },
  "practice": {
    "name": "Your Dental Practice",
    "phone": "+**********",
    "address": "123 Main St, City, State"
  }
}
```

### 6. Testing & Validation

#### Test Each Workflow:
1. **Appointment Reminders:**
   - Send test webhook with sample appointment data
   - Verify email and SMS delivery
   - Check timing accuracy

2. **New Patient Onboarding:**
   - Create test patient record
   - Verify welcome email sequence
   - Test form delivery timing

3. **Follow-up System:**
   - Trigger with completed treatment
   - Check post-care instructions
   - Verify review request timing

4. **Emergency System:**
   - Test with high pain level (8+)
   - Verify immediate alerts
   - Check on-call notifications

5. **Administrative:**
   - Test payment reminders
   - Verify insurance notifications
   - Check staff alerts

6. **Marketing:**
   - Test referral rewards
   - Verify promotional emails
   - Check reactivation campaigns

### 7. Go-Live Checklist

Before activating for all patients:

- [ ] All credentials tested and working
- [ ] Email templates reviewed and approved
- [ ] SMS messages comply with regulations
- [ ] Webhook endpoints responding correctly
- [ ] Staff trained on new processes
- [ ] Test patients completed successfully
- [ ] Backup procedures in place
- [ ] Monitoring dashboard configured

### 8. Monitoring & Maintenance

#### Daily Checks:
- Workflow execution status
- Error logs review
- Message delivery rates

#### Weekly Reviews:
- Performance metrics
- Patient feedback
- Staff feedback
- Optimization opportunities

#### Monthly Tasks:
- Update templates if needed
- Review automation effectiveness
- Adjust timing if necessary
- Plan new features

## Troubleshooting Common Issues

### Emails Not Sending:
1. Check SMTP credentials
2. Verify email service limits
3. Check spam folder
4. Review email content for spam triggers

### SMS Not Delivering:
1. Verify Twilio credentials
2. Check phone number format
3. Confirm SMS service balance
4. Review message content length

### Webhooks Not Triggering:
1. Test webhook URL manually
2. Check n8n workflow status
3. Verify JSON payload format
4. Review practice management system logs

### Timing Issues:
1. Check server timezone settings
2. Verify wait node configurations
3. Review appointment data format
4. Adjust timing based on practice needs

## Support Resources

- **Documentation:** `/documentation` folder
- **Video Tutorials:** Available upon request
- **Community Forum:** n8n community discussions
- **Direct Support:** Contact information in main README

---

**Next Steps:** Once setup is complete, proceed to the User Manual for daily operations guidance.
