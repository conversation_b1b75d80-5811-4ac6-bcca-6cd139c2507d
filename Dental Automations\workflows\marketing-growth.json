{"name": "Marketing & Growth Automation System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "marketing-trigger", "responseMode": "responseNode", "options": {}}, "id": "marketing-webhook", "name": "Marketing Trigger Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "marketing-trigger"}, {"parameters": {"values": {"string": [{"name": "campaign_type", "value": "={{ $json.campaign.type }}"}, {"name": "patient_name", "value": "={{ $json.patient.name || '' }}"}, {"name": "patient_email", "value": "={{ $json.patient.email || '' }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone || '' }}"}, {"name": "referrer_name", "value": "={{ $json.referral.referrer_name || '' }}"}, {"name": "referrer_email", "value": "={{ $json.referral.referrer_email || '' }}"}, {"name": "last_visit_date", "value": "={{ $json.patient.last_visit || '' }}"}, {"name": "treatment_interest", "value": "={{ $json.patient.treatment_interest || '' }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}, {"name": "practice_website", "value": "={{ $json.practice.website }}"}]}, "options": {}}, "id": "extract-marketing-data", "name": "Extract Marketing Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.campaign_type }}", "value2": "referral_reward"}]}}, "id": "check-campaign-type", "name": "Check Campaign Type", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"fromEmail": "referrals@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.referrer_email }}", "subject": "Thank You for Your Referral! 🎉", "text": "Dear {{ $json.referrer_name }},\n\nThank you so much for referring {{ $json.patient_name }} to {{ $json.practice_name }}! Your trust in our practice means the world to us.\n\n🎁 REFERRAL REWARD:\nAs a token of our appreciation, you've earned a $50 credit toward your next dental service! This credit will be automatically applied to your account.\n\n💫 REFERRAL PROGRAM BENEFITS:\n• $50 credit for each successful referral\n• No limit on referrals\n• Credits never expire\n• Can be used for any dental service\n\n👥 KEEP REFERRING:\nKnow someone else who could benefit from quality dental care? Keep those referrals coming! Every person you refer helps us grow our dental family.\n\n📞 Questions? Call us at {{ $json.practice_phone }}\n🌐 Learn more: {{ $json.practice_website }}\n\nThank you for being an amazing patient and advocate!\n\nWarm regards,\nThe {{ $json.practice_name }} Team"}, "id": "referral-reward-email", "name": "Referral Reward Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 150]}, {"parameters": {"fromEmail": "cosmetic@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Transform Your Smile - Special Cosmetic Offer! ✨", "text": "Hello {{ $json.patient_name }},\n\nReady to achieve the smile of your dreams? {{ $json.practice_name }} is here to help you transform your confidence with our advanced cosmetic dental services!\n\n✨ COSMETIC SERVICES WE OFFER:\n• Teeth <PERSON>ning (Professional & Take-Home)\n• Porcelain Veneers\n• Invisalign Clear Aligners\n• Dental Bonding\n• Smile Makeovers\n• Gum Contouring\n\n🎯 PERSONALIZED FOR YOU:\nBased on your interest in {{ $json.treatment_interest }}, we'd love to schedule a complimentary cosmetic consultation to discuss your goals and create a custom treatment plan.\n\n💰 LIMITED TIME OFFER:\n• FREE cosmetic consultation (valued at $150)\n• 15% off your first cosmetic treatment\n• Flexible payment plans available\n• Before/after photo documentation\n\n📅 BOOK YOUR CONSULTATION:\n🌐 Online: {{ $json.practice_website }}/cosmetic-consultation\n📞 Phone: {{ $json.practice_phone }}\n\nDon't wait - this special offer expires soon!\n\nYour dream smile awaits,\nThe {{ $json.practice_name }} Cosmetic Team"}, "id": "cosmetic-promotion-email", "name": "Cosmetic Promotion Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 250]}, {"parameters": {"fromEmail": "reactivation@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "We Miss You! Come Back for Better Oral Health 🦷", "text": "Dear {{ $json.patient_name }},\n\nWe noticed it's been a while since your last visit to {{ $json.practice_name }} on {{ $json.last_visit_date }}. We miss seeing your smile!\n\n🦷 WHY REGULAR DENTAL CARE MATTERS:\n• Prevents serious dental problems\n• Saves money on costly treatments\n• Maintains your beautiful smile\n• Protects your overall health\n• Early detection of issues\n\n🎁 WELCOME BACK SPECIAL:\nTo make it easy for you to return, we're offering:\n• 20% off your next cleaning\n• FREE oral health assessment\n• Updated digital X-rays (if needed)\n• Personalized treatment recommendations\n\n📅 EASY SCHEDULING:\n🌐 Book online: {{ $json.practice_website }}/schedule\n📞 Call us: {{ $json.practice_phone }}\n💬 Text us: Reply to this message\n\n⏰ FLEXIBLE APPOINTMENTS:\n• Morning, afternoon, and evening slots\n• Weekend appointments available\n• Same-day emergency care\n\nYour oral health is our priority. Let's get you back on track!\n\nWe look forward to seeing you soon,\nThe {{ $json.practice_name }} Team"}, "id": "reactivation-email", "name": "Patient Reactivation Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 350]}, {"parameters": {"fromEmail": "birthday@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Happy Birthday from {{ $json.practice_name }}! 🎂", "text": "🎉 Happy Birthday, {{ $json.patient_name }}! 🎉\n\nThe entire team at {{ $json.practice_name }} wants to wish you a fantastic birthday filled with joy, laughter, and beautiful smiles!\n\n🎁 BIRTHDAY GIFT FOR YOU:\nAs our birthday gift to you, enjoy:\n• 25% off any dental treatment this month\n• FREE teeth whitening consultation\n• Complimentary oral health goodie bag\n\n🦷 BIRTHDAY SMILE TIPS:\n• Keep smiling - it's your special day!\n• Don't forget to brush after birthday cake\n• Schedule your next cleaning if it's been a while\n• Share your beautiful smile with the world\n\n📞 Ready to use your birthday gift?\nCall us at {{ $json.practice_phone }} or visit {{ $json.practice_website }}\n\n🎂 FROM ALL OF US:\nThank you for being such a wonderful patient. We're grateful to be part of your dental health journey and hope your birthday is as bright as your smile!\n\nHappy Birthday!\nThe {{ $json.practice_name }} Family 🎈"}, "id": "birthday-email", "name": "Birthday Greeting Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 450]}, {"parameters": {"fromEmail": "newsletter@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "{{ $json.practice_name }} Monthly Newsletter - Oral Health Tips & Updates", "text": "{{ $json.practice_name }} Monthly Newsletter\n\n🦷 THIS MONTH'S ORAL HEALTH TIP:\nDid you know that your oral health is connected to your overall health? Regular dental checkups can help detect early signs of diabetes, heart disease, and other conditions.\n\n📰 PRACTICE NEWS:\n• New state-of-the-art equipment installed\n• Extended evening hours now available\n• New cosmetic treatment options\n• Staff spotlight: Meet our hygienist team\n\n🎯 FEATURED SERVICE:\nInvisalign Clear Aligners - Straighten your teeth discreetly! Ask about our current promotion during your next visit.\n\n📅 UPCOMING EVENTS:\n• Free oral health screenings - First Friday of the month\n• Children's dental education program\n• Senior citizen discount days\n\n💡 QUICK ORAL HEALTH TIPS:\n✓ Brush twice daily with fluoride toothpaste\n✓ Floss daily to remove plaque between teeth\n✓ Limit sugary and acidic foods\n✓ Don't use teeth as tools\n✓ Schedule regular checkups every 6 months\n\n📞 Contact Us:\nPhone: {{ $json.practice_phone }}\nWebsite: {{ $json.practice_website }}\n\nThank you for being part of our dental family!\nThe {{ $json.practice_name }} Team"}, "id": "newsletter-email", "name": "Monthly Newsletter", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 550]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Marketing campaign triggered\",\n  \"campaign_type\": \"{{ $json.campaign_type }}\",\n  \"patient\": \"{{ $json.patient_name }}\",\n  \"processed_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Marketing Trigger Webhook": {"main": [[{"node": "Extract Marketing Data", "type": "main", "index": 0}]]}, "Extract Marketing Data": {"main": [[{"node": "Check Campaign Type", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Check Campaign Type": {"main": [[{"node": "Referral Reward Email", "type": "main", "index": 0}], [{"node": "Cosmetic Promotion Email", "type": "main", "index": 0}], [{"node": "Patient Reactivation Email", "type": "main", "index": 0}], [{"node": "Birthday Greeting Email", "type": "main", "index": 0}], [{"node": "Monthly Newsletter", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "marketing-growth", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "marketing", "name": "marketing"}]}