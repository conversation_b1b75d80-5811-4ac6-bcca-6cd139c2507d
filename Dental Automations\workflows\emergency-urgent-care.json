{"name": "Emergency & Urgent Dental Care System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "emergency-request", "responseMode": "responseNode", "options": {}}, "id": "emergency-webhook", "name": "Emergency Request Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "emergency-request"}, {"parameters": {"values": {"string": [{"name": "patient_name", "value": "={{ $json.patient.name }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone }}"}, {"name": "patient_email", "value": "={{ $json.patient.email }}"}, {"name": "emergency_type", "value": "={{ $json.emergency.type }}"}, {"name": "pain_level", "value": "={{ $json.emergency.pain_level }}"}, {"name": "description", "value": "={{ $json.emergency.description }}"}, {"name": "is_existing_patient", "value": "={{ $json.patient.is_existing || false }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}, {"name": "on_call_doctor", "value": "={{ $json.practice.on_call_doctor }}"}, {"name": "on_call_phone", "value": "={{ $json.practice.on_call_phone }}"}]}, "options": {}}, "id": "extract-emergency-data", "name": "Extract Emergency Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.pain_level }}", "operation": "greaterEqual", "value2": "8"}]}}, "id": "check-severity", "name": "Check Emergency Severity", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.on_call_phone }}"}, {"name": "Body", "value": "🚨 URGENT DENTAL EMERGENCY 🚨\nPatient: {{ $json.patient_name }}\nPhone: {{ $json.patient_phone }}\nPain Level: {{ $json.pain_level }}/10\nType: {{ $json.emergency_type }}\nDescription: {{ $json.description }}\nExisting Patient: {{ $json.is_existing_patient ? 'Yes' : 'No' }}\nPlease respond ASAP!"}]}}, "id": "alert-on-call-doctor", "name": "<PERSON><PERSON>-<PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "{{ $json.patient_name }}, we've received your emergency request for {{ $json.emergency_type }}. Dr. {{ $json.on_call_doctor }} has been notified and will contact you within 15 minutes. For severe emergencies, call 911. {{ $json.practice_name }}"}]}}, "id": "immediate-patient-response", "name": "Immediate Patient Response", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"fromEmail": "emergency@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Emergency Dental Care - {{ $json.practice_name }}", "text": "Dear {{ $json.patient_name }},\n\nWe have received your emergency dental request and understand you're experiencing {{ $json.emergency_type }} with a pain level of {{ $json.pain_level }}/10.\n\n🚨 IMMEDIATE ACTIONS TAKEN:\n• Dr. {{ $json.on_call_doctor }} has been notified\n• You will receive a call within 15 minutes\n• Emergency appointment slot being prepared\n\n💊 TEMPORARY PAIN RELIEF (until you see the doctor):\n• Take over-the-counter pain medication as directed on package\n• Apply cold compress to outside of cheek (20 min on, 20 min off)\n• Rinse with warm salt water\n• Avoid extremely hot or cold foods/drinks\n\n⚠️ SEEK IMMEDIATE MEDICAL ATTENTION IF:\n• You have difficulty swallowing or breathing\n• You have a high fever (over 101°F)\n• You have severe facial swelling\n• You experience chest pain\n\n📞 Emergency Contact: {{ $json.on_call_phone }}\n📞 Practice Phone: {{ $json.practice_phone }}\n\nWe're here to help you through this emergency.\n\nDr. {{ $json.on_call_doctor }} and {{ $json.practice_name }} Team"}, "id": "emergency-care-email", "name": "Emergency Care Instructions Email", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "{{ $json.patient_name }}, your {{ $json.emergency_type }} request has been received. Our team will contact you within 2 hours to schedule an urgent appointment. For immediate relief, try over-the-counter pain medication and cold compress. {{ $json.practice_name }}"}]}}, "id": "standard-patient-response", "name": "Standard Patient Response", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"unit": "hours", "amount": 24}, "id": "wait-24h-followup", "name": "Wait 24h for Follow-up", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "Hi {{ $json.patient_name }}, this is {{ $json.practice_name }} following up on your emergency visit. How are you feeling today? Any ongoing pain or concerns? Please call {{ $json.practice_phone }} if you need further assistance."}]}}, "id": "emergency-followup-sms", "name": "Emergency Follow-up SMS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 600]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"emergency_received\",\n  \"message\": \"Emergency request processed\",\n  \"patient\": \"{{ $json.patient_name }}\",\n  \"emergency_type\": \"{{ $json.emergency_type }}\",\n  \"pain_level\": \"{{ $json.pain_level }}\",\n  \"response_time\": \"{{ $json.pain_level >= 8 ? '15 minutes' : '2 hours' }}\",\n  \"on_call_doctor\": \"Dr. {{ $json.on_call_doctor }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"Emergency Request Webhook": {"main": [[{"node": "Extract Emergency Data", "type": "main", "index": 0}]]}, "Extract Emergency Data": {"main": [[{"node": "Check Emergency Severity", "type": "main", "index": 0}, {"node": "Wait 24h for Follow-up", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Check Emergency Severity": {"main": [[{"node": "<PERSON><PERSON>-<PERSON>", "type": "main", "index": 0}, {"node": "Immediate Patient Response", "type": "main", "index": 0}, {"node": "Emergency Care Instructions Email", "type": "main", "index": 0}], [{"node": "Standard Patient Response", "type": "main", "index": 0}]]}, "Wait 24h for Follow-up": {"main": [[{"node": "Emergency Follow-up SMS", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "emergency-urgent-care", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "emergency", "name": "emergency"}]}