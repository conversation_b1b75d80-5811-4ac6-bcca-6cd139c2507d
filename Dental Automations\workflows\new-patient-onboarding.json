{"name": "New Patient Onboarding Automation", "nodes": [{"parameters": {"httpMethod": "POST", "path": "new-patient", "responseMode": "responseNode", "options": {}}, "id": "new-patient-webhook", "name": "New Patient Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "new-patient"}, {"parameters": {"values": {"string": [{"name": "patient_name", "value": "={{ $json.patient.name }}"}, {"name": "patient_email", "value": "={{ $json.patient.email }}"}, {"name": "patient_phone", "value": "={{ $json.patient.phone }}"}, {"name": "practice_name", "value": "={{ $json.practice.name }}"}, {"name": "practice_phone", "value": "={{ $json.practice.phone }}"}, {"name": "practice_address", "value": "={{ $json.practice.address }}"}, {"name": "doctor_name", "value": "={{ $json.practice.primary_doctor }}"}]}, "options": {}}, "id": "extract-patient-data", "name": "Extract Patient Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"fromEmail": "welcome@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Welcome to {{ $json.practice_name }}! Next Steps", "text": "Dear {{ $json.patient_name }},\n\nWelcome to {{ $json.practice_name }}! We're excited to have you as a new patient.\n\nTo help us provide you with the best care possible, please complete the following steps before your first appointment:\n\n1. Complete your patient intake forms (link will be sent shortly)\n2. Bring a valid ID and insurance card to your appointment\n3. Arrive 15 minutes early for check-in\n\nOur office information:\n📍 {{ $json.practice_address }}\n📞 {{ $json.practice_phone }}\n\nYour primary dentist will be Dr. {{ $json.doctor_name }}.\n\nIf you have any questions, please don't hesitate to contact us.\n\nWe look forward to seeing you soon!\n\nBest regards,\n{{ $json.practice_name }} Team", "attachments": []}, "id": "welcome-email", "name": "Send Welcome Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"unit": "minutes", "amount": 30}, "id": "wait-30min", "name": "Wait 30 Minutes", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 350]}, {"parameters": {"fromEmail": "forms@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Patient Intake Forms - {{ $json.practice_name }}", "text": "Hello {{ $json.patient_name }},\n\nPlease complete your patient intake forms before your first appointment. This will help us serve you better and reduce your wait time.\n\n📋 Medical History Form: [FORM_LINK_1]\n📋 Dental History Form: [FORM_LINK_2]\n📋 Insurance Information: [FORM_LINK_3]\n📋 Emergency Contact Form: [FORM_LINK_4]\n\nThese forms should take about 10-15 minutes to complete.\n\nIf you have any trouble accessing the forms, please call us at {{ $json.practice_phone }}.\n\nThank you,\n{{ $json.practice_name }} Team\n\nNote: Please complete these forms at least 24 hours before your appointment."}, "id": "intake-forms-email", "name": "Send Intake Forms", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 350]}, {"parameters": {"unit": "hours", "amount": 24}, "id": "wait-24h", "name": "Wait 24 Hours", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 500]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpHeaderAuth", "nodeCredentialType": "t<PERSON><PERSON><PERSON><PERSON>", "requestMethod": "POST", "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $credentials.twilioApi.accountSid }}/Messages.json", "sendBody": true, "bodyContentType": "form-urlencoded", "bodyParameters": {"parameters": [{"name": "From", "value": "={{ $credentials.twilioApi.phoneNumber }}"}, {"name": "To", "value": "={{ $json.patient_phone }}"}, {"name": "Body", "value": "Hi {{ $json.patient_name }}! Don't forget to complete your intake forms for {{ $json.practice_name }}. This will help speed up your first appointment. Need help? Call {{ $json.practice_phone }}"}]}}, "id": "forms-reminder-sms", "name": "Forms Reminder SMS", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 500]}, {"parameters": {"unit": "days", "amount": 3}, "id": "wait-3-days", "name": "Wait 3 Days", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [680, 650]}, {"parameters": {"fromEmail": "scheduling@{{ $json.practice_name.toLowerCase().replace(/\\s+/g, '') }}.com", "toEmail": "={{ $json.patient_email }}", "subject": "Ready to Schedule Your First Appointment?", "text": "Hello {{ $json.patient_name }},\n\nWe hope you've had a chance to complete your intake forms. We're ready to schedule your first appointment with Dr. {{ $json.doctor_name }}!\n\n🗓️ To schedule online: [SCHEDULING_LINK]\n📞 Or call us: {{ $json.practice_phone }}\n\nAvailable appointment types:\n• Comprehensive Exam & Cleaning (90 minutes)\n• Consultation Only (30 minutes)\n• Emergency/Urgent Care (60 minutes)\n\nWe recommend scheduling within the next 2 weeks for optimal care.\n\nWhat to bring:\n✓ Valid ID\n✓ Insurance card\n✓ List of current medications\n✓ Previous dental records (if available)\n\nWe look forward to meeting you!\n\nBest regards,\n{{ $json.practice_name }} Team"}, "id": "scheduling-follow-up", "name": "Scheduling Follow-up", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 650]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"New patient onboarding initiated\",\n  \"patient\": \"{{ $json.patient_name }}\",\n  \"next_steps\": \"Welcome email sent, intake forms to follow\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}], "connections": {"New Patient Webhook": {"main": [[{"node": "Extract Patient Data", "type": "main", "index": 0}]]}, "Extract Patient Data": {"main": [[{"node": "Send Welcome Email", "type": "main", "index": 0}, {"node": "Wait 30 Minutes", "type": "main", "index": 0}, {"node": "Wait 24 Hours", "type": "main", "index": 0}, {"node": "Wait 3 Days", "type": "main", "index": 0}, {"node": "Webhook Response", "type": "main", "index": 0}]]}, "Wait 30 Minutes": {"main": [[{"node": "Send Intake Forms", "type": "main", "index": 0}]]}, "Wait 24 Hours": {"main": [[{"node": "Forms Reminder SMS", "type": "main", "index": 0}]]}, "Wait 3 Days": {"main": [[{"node": "Scheduling Follow-up", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "new-patient-onboarding", "tags": [{"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "dental", "name": "dental"}, {"createdAt": "2025-01-01T00:00:00.000Z", "updatedAt": "2025-01-01T00:00:00.000Z", "id": "onboarding", "name": "onboarding"}]}